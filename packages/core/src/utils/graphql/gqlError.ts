/**
 * 自定义 GraphQL 错误类
 */
class GqlError extends Error {
  status: number
  data?: unknown
  type?: string
  retryMs?: number // 限流重试时间（毫秒）
  constructor(message: string, status: number, data?: unknown, type = 'default', retryMs?: number) {
    super(message)
    this.name = this.constructor.name // Set the error name to the name of the class
    this.status = status
    this.data = data
    this.type = type; // 自定义 error type
    this.retryMs = retryMs; // 限流重试时间
  }
}

export default GqlError
