/* ------------------------------- 与业务无关的辅助函数 ------------------------------- */

import { FetchBaseQueryError } from '@reduxjs/toolkit/query'
import { type ClassValue, clsx } from 'clsx'
import { isNumber, isObject } from 'lodash-es'
import { twMerge } from 'tailwind-merge'

import { URL_TYPE } from '../constants/global'
import { ButtonUrl } from '../typings/home'

import { getShopApiUrlBasicAuth } from './envUtil'

/**
 * 获取商城 Api URL 是否有 Auth 认证
 */
export const isShopUrlBasicAuth = !!getShopApiUrlBasicAuth()

/**
 * sleep
 */
export const sleep = async (ms: number) => {
  return new Promise((resolve) => setTimeout(resolve, ms))
}

/**
 * 判断当前是否是服务端环境
 */
export const isServer = typeof window === 'undefined' && typeof document === 'undefined'

/**
 * 判断数据是否是 base64 编码
 */
export const isBase64 = (data: string) => {
  const base64Regex = /^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/

  return base64Regex.test(data)
}

/**
 * 将数据进行 base64
 */
export const encodeBase64 = (data: string) => btoa(data)

/**
 * 对 base64 数据进行解析
 */
export const decodeBase64 = (data: string) => atob(data)

/**
 * 合并 header 对象
 */
export const mergeHeaders = (origin: HeadersInit, next: HeadersInit) => {
  if (
    origin instanceof Headers ||
    (isObject(origin) && next instanceof Headers) ||
    isObject(next)
  ) {
    const originHeaders = isObject(origin) ? new Headers(origin) : origin
    const nextHeaders = isObject(next) ? new Headers(next) : next

    for (const [key, value] of nextHeaders.entries()) {
      originHeaders.set(key, value)
    }

    return originHeaders
  }

  throw new Error('Input data error')
}

/**
 * 从字符集 'abcdefghijklmnopqrstuvwxyz0123456789' 中随机获取指定的位数
 */
export const generateRandomString = (length: number) => {
  const characters = 'abcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  const charactersLength = characters.length

  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * charactersLength)
    result += characters[randomIndex]
  }

  return result
}

/**
 * 格式化时间为天、小时、分钟、秒
 * @param {number} totalSeconds 剩余时间（单位：秒）
 * @returns {object} 格式化后的时间 { days, hours, minutes, seconds }
 */
export const formatTime = (totalSeconds: number) => {
  const days = Math.floor(totalSeconds / (60 * 60 * 24))
  const hours = Math.floor((totalSeconds % (60 * 60 * 24)) / (60 * 60))
  const minutes = Math.floor((totalSeconds % (60 * 60)) / 60)
  const seconds = Math.floor(totalSeconds % 60)

  return { total: totalSeconds, days, hours, minutes, seconds }
}

/**
 * 计算时间差
 */
export const calcLeftTime = (startTime: number, endTime: number) => {
  if ((startTime && isNumber(startTime)) || (endTime && isNumber(endTime))) {
    return Math.max(Number(endTime) - Number(startTime), 0)
  }

  return 0
}

/**
 * 判断传入的数值是否包含小数
 */
export const hasDecimal = (num: number | string) => Number(num) % 1 !== 0

/**
 * try catch error 类型
 */
export type TCatchMessage = Error | FetchBaseQueryError

/**
 * 限流错误信息类型
 */
export type TRateLimitError = {
  message: string
  retryMs: number
  isRateLimit: true
}

/**
 * 适配获取错误信息
 */
export const resolveCatchMessage = (error: TCatchMessage): string | TRateLimitError => {
  if (error instanceof Error) {
    return error.message
  }

  // 检查是否为精准限流错误
  if (error.data && typeof error.data === 'object' && 'errorType' in error.data) {
    const errorData = error.data as { errorType?: string; error?: string; retryMs?: number }
    if (errorData.errorType === 'precise-rate-limit' && errorData.retryMs) {
      return {
        message: errorData.error || '请求过于频繁，请稍后再试！',
        retryMs: errorData.retryMs,
        isRateLimit: true,
      }
    }
  }

  return error.data as string
}

type ClassNameValue = ClassNameArray | string | null | undefined | 0 | 0n | false
type ClassNameArray = ClassNameValue[]
/**
 * 合并样式
 */
export const mergeStyles = (...inputs: ClassNameValue[]) => {
  return twMerge(inputs)
}

/**
 * 从字符串中提取文件名
 */
export const extractFileName = (filePath: string) => {
  // 使用正则表达式匹配文件名部分
  const fileName = filePath.replace(/\.[^/.]+$/, '')
  return fileName
}

/**
 * 递归处理对象并转换为查询字符串
 * @param params 需要序列化的参数对象
 * @param prefix 前缀，用于递归处理嵌套对象
 * @returns 序列化后的查询字符串
 */
export const serializeQueryParams = (params: Record<string, string>, prefix = ''): string => {
  if (!isObject(params)) {
    return ''
  }

  return Object.keys(params)
    .map((key) => {
      const fullKey = prefix ? `${prefix}[${key}]` : key
      const value = params[key]

      if (isObject(value)) {
        return serializeQueryParams(value, fullKey) // 递归处理子对象
      }

      return `${encodeURIComponent(fullKey)}=${encodeURIComponent(value)}`
    })
    .join('&')
}

/**
 * 时间转换为时间戳 "2025-03-23 00:00:00"
 */
export const convertTimeToTimestamp = (time: string) => {
  if (!time || typeof time !== 'string') {
    return 0
  }

  const iosTime = time.replace(' ', 'T') + '+08:00'
  const date = new Date(iosTime)
  const timestampMilliseconds = date.getTime()
  return Math.floor(timestampMilliseconds / 1000)
}

/**
 * 解析pageParams
 */
export const parseToKeyValueObject = (pageParams: string) => {
  if (typeof pageParams === 'object') {
    return pageParams
  }

  if (typeof pageParams !== 'string' || !pageParams) {
    return {} // 返回空对象
  }

  // 去掉首尾的花括号并按逗号分割
  const keyValuePairs = pageParams.replace(/^{|}$/g, '').split(', ')

  // 转换为键值对对象
  const result = keyValuePairs.reduce(
    (acc, pair) => {
      // 按第一个等号分割，确保值中带有 "=" 不被误处理
      const indexOfEqual = pair.indexOf('=')
      const key = pair.slice(0, indexOfEqual)?.trim()
      const value = pair.slice(indexOfEqual + 1)?.trim()
      if (key) {
        // 确保key存在
        acc[key] = value
      }
      return acc
    },
    {} as Record<string, string>,
  ) // 明确指定对象类型为字符串键值对

  return result
}

/**
 * 根据url类型返回不同的事件数据
 */
export const generateEventParams = (url: ButtonUrl) => {
  const eventParams: Record<string, string> = {}

  if (url?.type === URL_TYPE.category) {
    eventParams.category_id = url?.value || ''
    eventParams.category_name = url?.label || ''
    return eventParams
  }

  if (url?.type === URL_TYPE.product) {
    eventParams.product_id = url?.value || ''
    eventParams.product_name = url?.label || ''
    return eventParams
  }

  return eventParams
}

/**
 * 生成banner点击事件参数
 */
export const generateBannerEventParams = (banner: {
  id?: string | number | null
  title?: string | null
  name?: string | null
}) => {
  return {
    banner_id: banner?.id || '',
    banner_name: banner?.title || banner?.name || '',
  }
}

/**
 * 生成搜索事件参数
 */
export const generateSearchEventParams = (searchWord: string) => {
  return {
    search_word: searchWord || '',
  }
}

/**
 * 生成按钮点击事件参数
 */
export const generateButtonEventParams = (buttonId?: string) => {
  return {
    button_id: buttonId || '',
  }
}

/**
 * 生成SKU相关事件参数
 */
export const generateSkuEventParams = (skuId?: string) => {
  return {
    sku_id: skuId || '',
  }
}

/**
 * 生成结算页面事件参数
 */
export const generateSettlementEventParams = (params: {
  cashAmount?: string | number
  nAmount?: string | number
}) => {
  return {
    cash_amount: String(params?.cashAmount || ''),
    N_amount: String(params?.nAmount || ''),
  }
}

/**
 * 生成支付方式事件参数
 */
export const generatePaymentEventParams = (paymentMethodId?: string) => {
  return {
    payment_method_id: paymentMethodId || '',
  }
}

/**
 * 生成金刚区点击事件参数
 */
export const generateKingkongEventParams = (kingkongName?: string) => {
  return {
    kingkong_name: kingkongName || '',
  }
}

/**
 * 合并 class
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * 格式化距离
 */
export const formatDistance = (distance: number) => {
  if (distance >= 1000) {
    return `${(distance / 1000).toFixed(2)} km`
  }
  return `${distance.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')} m`
}

/**
 * 格式化时间戳为可读时间格式
 */
export const formatTimestamp = (timestamp: number | string | null | undefined) => {
  if (!timestamp) return ''

  // 如果是字符串且已经是格式化的日期，则直接返回
  if (typeof timestamp === 'string' && isNaN(Number(timestamp))) {
    return timestamp
  }

  const date = new Date(
    typeof timestamp === 'string' ? parseInt(timestamp) * 1000 : timestamp * 1000,
  )
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  })
}

type TimeDiffResult = {
  days: number
  hours: number
  minutes: number
  seconds: number
  totalMs: number
}

/**
 * 获取时间差
 * @param start 开始时间
 * @param end 结束时间
 * @returns 时间差对象
 */
export function getTimeDiff(
  start: number | string | Date,
  end: number | string | Date,
): TimeDiffResult {
  const startTime = new Date(start).getTime()
  const endTime = new Date(end).getTime()

  const totalMs = Math.abs(endTime - startTime)
  let remaining = Math.floor(totalMs / 1000) // total seconds

  const days = Math.floor(remaining / (3600 * 24))
  remaining %= 3600 * 24

  const hours = Math.floor(remaining / 3600)
  remaining %= 3600

  const minutes = Math.floor(remaining / 60)
  const seconds = remaining % 60

  return { days, hours, minutes, seconds, totalMs }
}

/**
 * 检测是否为 Safari 浏览器
 */
export const isSafari = (): boolean => {
  if (typeof window === 'undefined') return false

  const userAgent = navigator.userAgent.toLowerCase()
  return userAgent.includes('safari') && !userAgent.includes('chrome')
}

/**
 * 检测是否为 iOS 设备
 */
export const isIOS = (): boolean => {
  if (typeof window === 'undefined') return false

  return /iPad|iPhone|iPod/.test(navigator.userAgent)
}
