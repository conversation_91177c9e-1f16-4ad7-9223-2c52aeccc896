import React from 'react'

import { useProductPrice } from '../../businessHooks'
import { PriceRanges } from '../../typings'
import { cn, mergeStyles } from '../../utils'
import { NCoinView } from '../ncoinView'

import styles from './styles'

type NCoinRangeProps = {
  priceRange: PriceRanges
  direction?: 'row' | 'column'
  iconStyle?: object
  textStyle?: string
  originTextStyle?: string
  originIconStyle?: object
  gapStyle?: string
}

/**
 * NCoinRange 组件
 *
 * 用于展示产品的N币价格，支持同时显示折扣N币价和原N币价。
 *
 * @param {Object} props - 组件参数。
 * @param {Object} props.priceRange - 产品价格范围信息。
 * @param {Object} props.priceRange.finalPrice - 最终价格信息。
 * @param {number} props.priceRange.finalPrice.value - 最终价格的数值。
 * @param {Object} props.priceRange.regularPrice - 原价信息。
 * @param {number} props.priceRange.regularPrice.value - 原价的数值。
 * @param {'row' | 'column'} [props.direction='row'] - 展示价格的方向，'row' 为横向，'column' 为纵向，默认为横向。
 * @param {Object} [props.textStyle=''] - 自定义N币文本的样式。
 * @param {Object} [props.iconStyle=''] - 自定义N币图标的样式。
 * @param {Object} [props.originTextStyle=''] - 自定义划线N币文本的样式。
 * @param {Object} [props.originIconStyle=''] - 自定义划线N币图标的样式。
 *
 * @returns {JSX.Element} PriceRange 组件。
 */
const NCoinRange = (props: NCoinRangeProps) => {
  const {
    priceRange,
    direction = 'row',
    textStyle = '',
    iconStyle,
    originTextStyle = '',
    originIconStyle = '',
    gapStyle = '',
  } = props

  const { isDiscount, finalPrice, regularPrice } = useProductPrice(priceRange)

  return (
    <div className={mergeStyles([styles.root, direction === 'column' ? styles.rootColumn : ''])}>
      {isDiscount && (
        <NCoinView
          number={Number(finalPrice.value)}
          iconStyle={
            isDiscount
              ? { background: '#DA291C', ...iconStyle }
              : { background: '#000000', ...iconStyle }
          }
          textStyle={cn([styles.priceText, textStyle, isDiscount ? 'text-primary' : 'text-black'])}
        />
      )}
      {direction === 'row' && isDiscount ? <div className={cn(styles.gap, gapStyle)} /> : null}
      <NCoinView
        iconStyle={
          isDiscount
            ? Object.assign(
                {
                  background: '#86868B',
                  size: 14,
                },
                originIconStyle,
              )
            : iconStyle
        }
        textStyle={
          isDiscount
            ? mergeStyles([styles.priceText, styles.regularPriceText, originTextStyle])
            : mergeStyles([styles.priceText, textStyle])
        }
        number={Number(regularPrice.value)}
      />
    </div>
  )
}

export default NCoinRange
