import React from 'react'

import { useProductPrice } from '../../businessHooks'
import { PriceRanges } from '../../typings'
import { cn, mergeStyles } from '../../utils'
import Price from '../price/Price'

/**
 * PriceRange 组件
 *
 * 用于展示产品的价格范围，支持同时显示折扣价和原价。
 *
 * @param {Object} props - 组件参数。
 * @param {Object} props.priceRange - 产品价格范围信息。
 * @param {Object} props.priceRange.finalPrice - 最终价格信息（折扣后价格）。
 * @param {string} props.priceRange.finalPrice.currency - 货币代码（例如 'CNY', 'USD'）。
 * @param {number} props.priceRange.finalPrice.value - 最终价格的数值。
 * @param {Object} props.priceRange.regularPrice - 原价信息。
 * @param {string} props.priceRange.regularPrice.currency - 货币代码（例如 'CNY', 'USD'）。
 * @param {number} props.priceRange.regularPrice.value - 原价的数值。
 * @param {'row' | 'column'} [props.direction='row'] - 展示价格的方向，'row' 为横向，'column' 为纵向，默认为横向。
 * @param {Object} [props.currencyStyle={}] - 自定义货币符号的样式。
 * @param {Object} [props.textStyle={}] - 自定义价格文本的样式。
 * @param {Object} [props.fractionStyle={}] - 自定义小数部分的样式。
 *
 * @returns {JSX.Element} PriceRange 组件。
 */

type PriceRangeProps = {
  priceRange: PriceRanges
  direction?: 'row' | 'column'
  rootStyle?: string
  gapStyle?: string
  currencyStyle?: string
  textStyle?: string
  fractionStyle?: string
  originTextStyle?: string
  originCurrencyStyle?: string
  originFractionStyle?: string
}

const PriceRange = (props: PriceRangeProps) => {
  const {
    priceRange,
    direction = 'row',
    rootStyle = '',
    gapStyle = '',
    currencyStyle = '',
    textStyle = '',
    fractionStyle = '',
    originTextStyle = '',
    originCurrencyStyle = '',
    originFractionStyle = '',
  } = props

  const { isDiscount, finalPrice, regularPrice } = useProductPrice(priceRange)

  return (
    <div
      className={mergeStyles([
        'flex items-baseline',
        direction === 'column' ? 'flex-col' : '',
        rootStyle,
      ])}>
      {isDiscount && (
        <Price
          bold={true}
          textStyle={textStyle}
          fractionStyle={fractionStyle}
          currencyStyle={currencyStyle}
          color="primary"
          price={finalPrice}
        />
      )}
      {direction === 'row' && isDiscount ? <div className={cn('w-2', gapStyle)} /> : null}
      <Price
        bold
        currencyStyle={
          isDiscount ? mergeStyles(['text-4 leading-[12px]', originCurrencyStyle]) : currencyStyle
        }
        textStyle={
          isDiscount ? mergeStyles(['text-base leading-[1.2]', originTextStyle]) : textStyle
        }
        fractionStyle={
          isDiscount ? mergeStyles(['text-4 leading-[12px]', originFractionStyle]) : fractionStyle
        }
        color={isDiscount ? 'gray' : 'default'}
        variant={isDiscount ? 'lineThrough' : ''}
        price={regularPrice}
      />
    </div>
  )
}

export default PriceRange
