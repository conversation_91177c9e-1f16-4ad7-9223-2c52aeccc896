import React, { useCallback, useMemo } from 'react'

import { CurrencyEnum } from '../../graphql/generated/graphql'
import { mergeStyles, patches } from '../../utils'

/**
 * Price 组件
 *
 * 用于展示格式化的价格，支持多种样式和配置选项。该组件根据传入的价格对象，
 * 使用国际化的方式格式化并渲染价格，支持货币符号、价格小数部分、颜色变体等样式设置。
 *
  @param {Object} props - 组件参数。
 * @param {Object} props.price - 价格对象，包含货币和数值。
 * @param {string} [props.price.currency] - 货币代码（例如 'CNY', 'USD'）。如果未提供，则默认使用从 `useIntl` 获取的货币。
 * @param {number} [props.price.value=0] - 价格的数值。如果未提供，默认为 0。
 * @param {'default' | 'primary' | 'gray'} [props.color='default'] - 价格的颜色样式，支持 'default'（默认）、'primary'（主色）、'gray'（灰色）。
 * @param {boolean} [props.bold=false] - 是否加粗显示价格。
 * @param {'lineThrough' | ''} [props.variant=''] - 价格的样式变体，例如 'lineThrough' 表示带删除线的样式。
 * @param {boolean} [props.showCurrency=true] - 是否显示货币符号，默认为 `true`。
 * @param {boolean} [props.showFraction=true] - 是否显示价格的小数部分，默认为 `true`。
 * @param {Object} [props.containerStyle=''] - 自定义外层容器的样式。
 * @param {Object} [props.currencyStyle=''] - 自定义货币符号的样式。
 * @param {Object} [props.textStyle=''] - 自定义价格文本的样式。
 * @param {Object} [props.fractionStyle=''] - 自定义小数部分的样式。
 * @param {boolean} [props.block=true] - 控制组件是否以块级元素形式展示。如果为 `false`，组件将作为内联元素显示。
 * @param {string} [props.symbolStyle] - 自定义货币符号的样式。
 *
 * @returns {JSX.Element} Price 组件。
 */

type PriceProps = {
  price?: {
    currency?: CurrencyEnum | null
    value?: number | null
  }
  color?: 'default' | 'primary' | 'gray' | 'white'
  bold?: boolean
  variant?: 'lineThrough' | ''
  showCurrency?: boolean
  showFraction?: boolean
  containerStyle?: string
  currencyStyle?: string
  textStyle?: string
  fractionStyle?: string
  block?: boolean
  symbolStyle?: string
}

const styles = {
  root: 'flex flex-row items-baseline',
  boldText: 'font-miSansDemiBold450',
  defaultPriceText: 'text-[16px] leading-[19px] text-[#000000]',
  lineThroughPriceText: 'line-through',
  currencyText: 'text-[12px] leading-[14px] text-[#000000] mr-[4px] font-miSansMedium450',
  fractionStyle: 'text-[12px] leading-[14px]',
}

const Price = (props: PriceProps) => {
  const {
    price,
    color = 'default',
    bold = false,
    variant = '',
    showCurrency = true,
    showFraction = true,
    containerStyle = '',
    currencyStyle: propsCurrencyStyle = '',
    textStyle = '',
    fractionStyle: propsFractionStyle = '',
    symbolStyle = '',
  } = props

  const parts = useMemo(() => {
    return patches.toParts.call(
      new Intl.NumberFormat('zh-CN', { style: 'currency', currency: price?.currency ?? 'CNY' }),
      price?.value ?? 0,
    )
  }, [price])

  const colorStyle = useMemo(() => {
    let style = '#000000'

    switch (color) {
      case 'primary':
        style = '#DA291C'
        break
      case 'gray':
        style = '#86868B'
        break
      case 'white':
        style = '#FFFFFF'
        break
      default:
        break
    }

    return style
  }, [color])

  const renderContent = useCallback(() => {
    return (
      <div className={`flex flex-row text-[${colorStyle}] items-baseline justify-center`}>
        {(price?.value ?? 0) < 0 && (
          <span
            className={mergeStyles(`text-[${colorStyle}]`, bold && styles.boldText, symbolStyle)}>
            -
          </span>
        )}

        {showCurrency && (
          <span
            className={mergeStyles(
              styles.currencyText,
              `text-[${colorStyle}]`,
              bold && styles.boldText,
              propsCurrencyStyle,
            )}>
            {parts?.find((part) => part.type === 'currency')?.value}
          </span>
        )}

        <span
          className={mergeStyles(
            'flex flex-row items-baseline justify-center',
            variant === 'lineThrough' && styles.lineThroughPriceText,
          )}>
          {parts
            .filter((part) => !['decimal', 'fraction', 'minusSign', 'currency'].includes(part.type))
            .map((part, i) => (
              <span
                key={`${i}-${part.type}`}
                className={mergeStyles(
                  styles.defaultPriceText,
                  `text-[${colorStyle}]`,
                  bold && styles.boldText,
                  textStyle,
                )}>
                {part.value}
              </span>
            ))}
        </span>

        {showFraction && (
          <span
            className={mergeStyles(
              'flex flex-row items-baseline justify-center',
              variant === 'lineThrough' && styles.lineThroughPriceText,
            )}>
            {parts.slice(parts.findIndex((part) => part.type === 'decimal')).map((fraction, i) => (
              <span
                key={`${i}-${fraction.type}`}
                className={mergeStyles(
                  styles.defaultPriceText,
                  styles.fractionStyle,
                  `text-[${colorStyle}]`,
                  bold && styles.boldText,
                  propsFractionStyle,
                )}>
                {fraction.value}
              </span>
            ))}
          </span>
        )}
      </div>
    )
  }, [
    parts,
    price,
    colorStyle,
    bold,
    variant,
    showCurrency,
    showFraction,
    propsCurrencyStyle,
    textStyle,
    propsFractionStyle,
    symbolStyle,
  ])

  return <div className={mergeStyles(styles.root, containerStyle)}>{renderContent()}</div>
}

export default Price
