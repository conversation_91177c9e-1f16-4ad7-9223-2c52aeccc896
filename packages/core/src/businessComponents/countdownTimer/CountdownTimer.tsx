'use client'
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { useTranslations } from 'next-intl'

import { useTimerCoundown } from '../../businessHooks'
import { mergeStyles } from '../../utils'

import styles from './styles'

/**
 * @description CountdownTimer component
 * @param {Object} props - 组件属性
 * @param {ReactNode} props.header - 组件头部，可以是任何 React 组件
 * @param {Object} props.headerStyle - 头部样式对象
 * @param {string} props.text - 倒计时文本
 * @param {Object} props.textStyle - 倒计时文本样式
 * @param {Object} props.timeBoxStyle - 时间盒子样式
 * @param {string} props.colonText - 分隔符文本
 * @param {Object} props.colonStyle - 分隔符样式
 * @param {Object} props.timeTextStyle - 时间文本样式
 * @param {string} props.startTime 服务器时间，时间戳
 * @param {string} props.activityStartTime - 活动开始时间，时间戳
 * @param {string} props.targetTime - 目标时间，活动结束时间，时间戳
 * @param {Object} props.timeContainerStyle - 时间容器样式
 * @param {string} props.type - 类型，默认为 'default', 还有 'vertical_day', 'horizontal_day'
 * @param {string} props.typeText - 类型文本
 * @param {Object} props.containerStyle - 容器样式
 */

type CountdownTimerProps = {
  header?: React.ReactNode
  headerStyle?: string
  text?: 'time_left'
  beforeStartText?: 'time_to_start'
  textStyle?: string
  timeBoxStyle?: string
  timeContainerStyle?: string
  colonText?: string
  colonStyle?: string
  timeTextStyle?: string
  startTime: string
  activityStartTime: string
  targetTime: string
  type?: 'default' | 'vertical_day' | 'horizontal_day'
  typeText?: string
  containerStyle?: string
  headerTitleStyle?: string
  verticalBoxStyle?: string
  verticalTextBoxStyle?: string
  verticalTextStyle?: string
  verticalTimeSize?: number
  verticalTextWrapperStyle?: string
  verticalTimeContentBoxStyle?: string
  verticalTextContainerStyle?: string
}

const CountdownTimer = ({
  header,
  headerStyle = '',
  text = 'time_left',
  beforeStartText = 'time_to_start',
  textStyle = '',
  timeBoxStyle = '',
  colonText = ':',
  colonStyle = '',
  timeTextStyle = '',
  startTime,
  activityStartTime,
  targetTime,
  timeContainerStyle = '',
  type = 'default',
  typeText = '',
  containerStyle = '',
  headerTitleStyle = '',
  verticalBoxStyle = '',
  verticalTextBoxStyle = '',
  verticalTextStyle = '',
  verticalTimeSize = 16,
  verticalTextWrapperStyle = '',
  verticalTimeContentBoxStyle = '',
  verticalTextContainerStyle = '',
}: CountdownTimerProps) => {
  const getI18nString = useTranslations('Common')
  const [showTimer, setShowTimer] = useState(true)
  const [currentMode, setCurrentMode] = useState(
    activityStartTime && activityStartTime > startTime ? 'beforeStart' : 'beforeEnd',
  )
  const [currentTime, setCurrentTime] = useState(startTime)

  const onEnd = useCallback(() => {
    if (currentMode === 'beforeStart') {
      setCurrentMode('beforeEnd')
      setCurrentTime(activityStartTime)
    } else {
      setShowTimer(false)
    }
  }, [currentMode, activityStartTime])

  const countdownTargetTime = useMemo(
    () => (currentMode === 'beforeStart' ? activityStartTime : targetTime),
    [currentMode, activityStartTime, targetTime],
  )

  const countdownText = useMemo(
    () => (currentMode === 'beforeStart' ? beforeStartText : text),
    [currentMode, beforeStartText, text],
  )

  useEffect(() => {
    setCurrentTime(startTime)
    setCurrentMode(activityStartTime && activityStartTime > startTime ? 'beforeStart' : 'beforeEnd')
  }, [activityStartTime, startTime])

  const remainingTime = useTimerCoundown(
    Number(currentTime),
    Number(countdownTargetTime),
    1000,
    onEnd,
  )

  const formatTime = useCallback(
    (time: { days: number; hours: number; minutes: number; seconds: number }) => {
      const { days, hours, minutes, seconds } = time

      let newHours = hours

      if (type === 'default') {
        newHours = days * 24 + hours
      }

      const pad = (num: number) => String(num).padStart(2, '0')
      return [pad(days), pad(newHours), pad(minutes), pad(seconds)]
    },
    [type],
  )

  const [days, hours, minutes, seconds] = formatTime(remainingTime)

  const renderTimer = useCallback(() => {
    if (type === 'vertical_day') {
      return (
        <>
          {typeText && (
            <div className={mergeStyles([styles.headerTitle, headerTitleStyle])}>{typeText}</div>
          )}
          <div className={mergeStyles([styles.verticalTimeContainer, timeContainerStyle])}>
            <div
              className={mergeStyles([styles.verticalTimeContentBox, verticalTimeContentBoxStyle])}>
              <div className={mergeStyles([styles.verticalTimeContentText, textStyle])}>
                {getI18nString(countdownText)}
              </div>
            </div>
            <div className={styles.verticalTimer}>
              <div className={mergeStyles([styles.verticalBox, verticalBoxStyle])}>
                <div className={mergeStyles([styles.verticalTimeBox, timeBoxStyle])}>
                  <div
                    className={mergeStyles([
                      styles.verticalTextWrapper,
                      verticalTextWrapperStyle,
                      `w-[${days.length * verticalTimeSize}px]`,
                    ])}>
                    <div className={mergeStyles([styles.verticalTimeText, timeTextStyle])}>
                      {days}
                    </div>
                  </div>
                </div>
              </div>

              <div className={mergeStyles([styles.verticalColon, colonStyle])}>{colonText}</div>
              <div className={mergeStyles([styles.verticalBox, verticalBoxStyle])}>
                <div className={mergeStyles([styles.verticalTimeBox, timeBoxStyle])}>
                  <div
                    className={mergeStyles([styles.verticalTextWrapper, verticalTextWrapperStyle])}>
                    <div className={mergeStyles([styles.verticalTimeText, timeTextStyle])}>
                      {hours}
                    </div>
                  </div>
                </div>
              </div>

              <div className={mergeStyles([styles.verticalColon, colonStyle])}>{colonText}</div>
              <div className={mergeStyles([styles.verticalBox, verticalBoxStyle])}>
                <div className={mergeStyles([styles.verticalTimeBox, timeBoxStyle])}>
                  <div
                    className={mergeStyles([styles.verticalTextWrapper, verticalTextWrapperStyle])}>
                    <div className={mergeStyles([styles.verticalTimeText, timeTextStyle])}>
                      {minutes}
                    </div>
                  </div>
                </div>
              </div>

              <div className={mergeStyles([styles.verticalColon, colonStyle])}>{colonText}</div>
              <div className={mergeStyles([styles.verticalBox, verticalBoxStyle])}>
                <div className={mergeStyles([styles.verticalTimeBox, timeBoxStyle])}>
                  <div
                    className={mergeStyles([styles.verticalTextWrapper, verticalTextWrapperStyle])}>
                    <div className={mergeStyles([styles.verticalTimeText, timeTextStyle])}>
                      {seconds}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className={mergeStyles([styles.verticalTextBox, verticalTextContainerStyle])}>
              <div className={mergeStyles([styles.verticalTextBoxWrapper, verticalTextBoxStyle])}>
                <div className={mergeStyles([styles.verticalText, verticalTextStyle])}>DAY</div>
              </div>
              <div className={mergeStyles([styles.verticalTextBoxWrapper, verticalTextBoxStyle])}>
                <div className={mergeStyles([styles.verticalText, verticalTextStyle])}>HOUR</div>
              </div>
              <div className={mergeStyles([styles.verticalTextBoxWrapper, verticalTextBoxStyle])}>
                <div className={mergeStyles([styles.verticalText, verticalTextStyle])}>MINUTE</div>
              </div>
              <div className={mergeStyles([styles.verticalTextBoxWrapper, verticalTextBoxStyle])}>
                <div className={mergeStyles([styles.verticalText, verticalTextStyle])}>SECOND</div>
              </div>
            </div>
          </div>
        </>
      )
    }

    if (type === 'horizontal_day') {
      return (
        <>
          {typeText && (
            <div className={mergeStyles([styles.headerTitle, headerTitleStyle])}>{typeText}</div>
          )}
          <div className={mergeStyles([styles.horizontalTimeContainer, timeContainerStyle])}>
            <div className={styles.horizontalTimeContentBox}>
              <div className={mergeStyles([styles.horizontalTimeContentText, textStyle])}>
                {getI18nString(countdownText)}
              </div>
            </div>
            <div className={mergeStyles([styles.horizontalTimeBox, timeBoxStyle])}>
              <div
                className={mergeStyles([
                  styles.horizontalTextWrapper,
                  `w-[${days.length * 14}px]`,
                ])}>
                <div className={mergeStyles([styles.horizontalTimeText, timeTextStyle])}>
                  {days}
                </div>
              </div>
            </div>
            <div className={mergeStyles([styles.horizontalColon, colonStyle])}>
              {getI18nString('day')}
            </div>
            <div className={mergeStyles([styles.horizontalTimeBox, timeBoxStyle])}>
              <div className={styles.horizontalTextWrapper}>
                <div className={mergeStyles([styles.horizontalTimeText, timeTextStyle])}>
                  {hours}
                </div>
              </div>
            </div>
            <div className={mergeStyles([styles.horizontalColon, colonStyle])}>
              {getI18nString('hour')}
            </div>
            <div className={mergeStyles([styles.horizontalTimeBox, timeBoxStyle])}>
              <div className={styles.horizontalTextWrapper}>
                <div className={mergeStyles([styles.horizontalTimeText, timeTextStyle])}>
                  {minutes}
                </div>
              </div>
            </div>
            <div className={mergeStyles([styles.horizontalColon, colonStyle])}>
              {getI18nString('minute')}
            </div>
            <div className={mergeStyles([styles.horizontalTimeBox, timeBoxStyle])}>
              <div className={styles.horizontalTextWrapper}>
                <div className={mergeStyles([styles.horizontalTimeText, timeTextStyle])}>
                  {seconds}
                </div>
              </div>
            </div>
            <div className={mergeStyles([styles.horizontalColon, colonStyle])}>
              {getI18nString('second')}
            </div>
          </div>
        </>
      )
    }

    return (
      <div className={mergeStyles([styles.timeContainer, timeContainerStyle])}>
        <div className={styles.timeContentBox}>
          <div className={mergeStyles([styles.timeContentText, textStyle])}>
            {getI18nString(countdownText)}
          </div>
        </div>
        <div className={mergeStyles([styles.timeBox, timeBoxStyle])}>
          <div className={mergeStyles([styles.textWrapper, `w-[${hours.length * 10}px]`])}>
            <div className={mergeStyles([styles.timeText, timeTextStyle])}>{hours}</div>
          </div>
        </div>
        <div className={mergeStyles([styles.colon, colonStyle])}>{colonText}</div>
        <div className={mergeStyles([styles.timeBox, timeBoxStyle])}>
          <div className={styles.textWrapper}>
            <div className={mergeStyles([styles.timeText, timeTextStyle])}>{minutes}</div>
          </div>
        </div>
        <div className={mergeStyles([styles.colon, colonStyle])}>{colonText}</div>
        <div className={mergeStyles([styles.timeBox, timeBoxStyle])}>
          <div className={styles.textWrapper}>
            <div className={mergeStyles([styles.timeText, timeTextStyle])}>{seconds}</div>
          </div>
        </div>
      </div>
    )
  }, [
    type,
    timeContainerStyle,
    textStyle,
    timeBoxStyle,
    timeTextStyle,
    colonStyle,
    colonText,
    typeText,
    countdownText,
    days,
    hours,
    minutes,
    seconds,
    getI18nString,
    verticalTextBoxStyle,
    verticalBoxStyle,
    verticalTextStyle,
    headerTitleStyle,
    verticalTimeSize,
    verticalTextContainerStyle,
    verticalTimeContentBoxStyle,
    verticalTextWrapperStyle,
  ])

  return showTimer ? (
    <div className={mergeStyles([styles.container, containerStyle])}>
      {header && <div className={headerStyle}>{header}</div>}
      {renderTimer()}
    </div>
  ) : null
}

export default CountdownTimer
