import React from 'react'
import { isUndefined } from 'lodash-es'

import { IconNCoin } from '../../components'
import { mergeStyles } from '../../utils'

/**
 * N币数量展示组件
 *
 * @param {Object} props 组件的属性
 * @param {number} props.number N币的数量。如果未传入（undefined），组件将不渲染内容。
 * @param {Object} [props.style] 自定义的根容器样式，覆盖或合并默认样式。
 * @param {Object} [props.iconStyle] 自定义的图标样式，覆盖或合并默认的文本样式。
 * @param {Object} [props.textStyle] 自定义的文字样式，覆盖或合并默认的文本样式。
 * @param {string} [props.prefixText] 自定义的前缀文本。
 * @param {string} [props.prefixTextStyle] 自定义的文字样式，覆盖或合并默认的文本样式。
 * @param {boolean} [props.showZero=false] - 是否展示0。
 * @returns {JSX.Element} N币数量展示组件。
 */

type NCoinViewProps = {
  number: number
  style?: string
  iconStyle?: {
    background?: string
    size?: number
    color?: string
  }
  textStyle?: string
  prefixText?: string
  prefixTextStyle?: string
  showZero?: boolean
}

const NCoinView = (props: NCoinViewProps) => {
  const {
    number,
    style = '',
    iconStyle,
    textStyle = '',
    prefixText,
    prefixTextStyle = '',
    showZero = false,
  } = props

  if (isUndefined(number) || (Number(number) <= 0 && !showZero)) {
    return null
  }

  return (
    <div className={mergeStyles(['flex flex-wrap items-center', style])}>
      {prefixText ? (
        <span
          className={mergeStyles([
            'font-miSansMedium450 pr-[4px] text-[12px] leading-[16px] text-[#444446]',
            prefixTextStyle,
          ])}>
          {prefixText}
        </span>
      ) : null}
      <div className="flex items-center">
        <IconNCoin
          background={iconStyle?.background}
          color={iconStyle?.color}
          size={iconStyle?.size}
        />
        <span
          className={mergeStyles([
            'ml-[2px] font-miSansDemiBold450 text-[14px] leading-[17px] text-[#000000]',
            textStyle,
          ])}>
          {number}
        </span>
      </div>
    </div>
  )
}

export default NCoinView
