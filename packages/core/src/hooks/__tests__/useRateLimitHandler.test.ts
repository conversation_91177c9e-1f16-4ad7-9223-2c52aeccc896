import { renderHook, act } from '@testing-library/react'
import { useRateLimitHandler } from '../useRateLimitHandler'
import { resolveCatchMessage } from '../../utils'

// Mock dependencies
jest.mock('../../components/toast', () => ({
  useToastContext: () => ({
    show: jest.fn(),
    hide: jest.fn(),
  }),
}))

jest.mock('../../utils', () => ({
  resolveCatchMessage: jest.fn(),
}))

const mockResolveCatchMessage = resolveCatchMessage as jest.MockedFunction<typeof resolveCatchMessage>

describe('useRateLimitHandler', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    jest.useFakeTimers()
  })

  afterEach(() => {
    jest.useRealTimers()
  })

  it('should handle rate limit error in button mode', () => {
    const { result } = renderHook(() =>
      useRateLimitHandler({
        mode: 'button',
        originalText: 'Submit',
      })
    )

    // Mock rate limit error
    const rateLimitError = {
      message: '请求过于频繁',
      retryMs: 5000,
      isRateLimit: true,
    }
    mockResolveCatchMessage.mockReturnValue(rateLimitError)

    const mockError = new Error('Rate limit error')

    act(() => {
      const handled = result.current.handleError(mockError)
      expect(handled).toBe(true)
    })

    // Should be rate limited
    expect(result.current.isRateLimited).toBe(true)
    expect(result.current.isDisabled).toBe(true)
    expect(result.current.countdown).toBe(5)
    expect(result.current.buttonText).toBe('Submit（5s）')
  })

  it('should handle rate limit error in toast mode', () => {
    const { result } = renderHook(() =>
      useRateLimitHandler({
        mode: 'toast',
      })
    )

    // Mock rate limit error
    const rateLimitError = {
      message: '请求过于频繁',
      retryMs: 3000,
      isRateLimit: true,
    }
    mockResolveCatchMessage.mockReturnValue(rateLimitError)

    const mockError = new Error('Rate limit error')

    act(() => {
      const handled = result.current.handleError(mockError)
      expect(handled).toBe(true)
    })

    // Should not be disabled in toast mode
    expect(result.current.isRateLimited).toBe(false)
    expect(result.current.isDisabled).toBe(false)
  })

  it('should not handle non-rate-limit error', () => {
    const { result } = renderHook(() =>
      useRateLimitHandler({
        mode: 'button',
        originalText: 'Submit',
      })
    )

    // Mock regular error
    mockResolveCatchMessage.mockReturnValue('Regular error message')

    const mockError = new Error('Regular error')

    act(() => {
      const handled = result.current.handleError(mockError)
      expect(handled).toBe(false)
    })

    // Should not be rate limited
    expect(result.current.isRateLimited).toBe(false)
    expect(result.current.isDisabled).toBe(false)
  })

  it('should countdown and reset after timeout', () => {
    const { result } = renderHook(() =>
      useRateLimitHandler({
        mode: 'button',
        originalText: 'Submit',
      })
    )

    // Mock rate limit error
    const rateLimitError = {
      message: '请求过于频繁',
      retryMs: 3000,
      isRateLimit: true,
    }
    mockResolveCatchMessage.mockReturnValue(rateLimitError)

    const mockError = new Error('Rate limit error')

    act(() => {
      result.current.handleError(mockError)
    })

    expect(result.current.countdown).toBe(3)

    // Fast forward 1 second
    act(() => {
      jest.advanceTimersByTime(1000)
    })

    expect(result.current.countdown).toBe(2)

    // Fast forward to end
    act(() => {
      jest.advanceTimersByTime(2000)
    })

    expect(result.current.isRateLimited).toBe(false)
    expect(result.current.countdown).toBe(0)
  })

  it('should reset state when reset is called', () => {
    const { result } = renderHook(() =>
      useRateLimitHandler({
        mode: 'button',
        originalText: 'Submit',
      })
    )

    // Mock rate limit error
    const rateLimitError = {
      message: '请求过于频繁',
      retryMs: 5000,
      isRateLimit: true,
    }
    mockResolveCatchMessage.mockReturnValue(rateLimitError)

    const mockError = new Error('Rate limit error')

    act(() => {
      result.current.handleError(mockError)
    })

    expect(result.current.isRateLimited).toBe(true)

    act(() => {
      result.current.reset()
    })

    expect(result.current.isRateLimited).toBe(false)
    expect(result.current.countdown).toBe(0)
  })
})
