"use client"

import { useMemo } from 'react';

import { PriceRanges } from '../typings';

/**
 * useProductPrice Hook
 *
 * 用于从价格范围对象中提取价格信息，包括是否存在促销价、最终价格、原价以及货币信息。
 *
 * @param {Object} priceRange - 价格范围对象。
 * @param {Object} priceRange.maximum_price - 最大价格信息。
 * @param {Object} priceRange.maximum_price.discount - 折扣信息。
 * @param {number} priceRange.maximum_price.discount.amount_off - 折扣金额。
 * @param {Object} priceRange.maximum_price.final_price - 最终价格对象。
 * @param {string} priceRange.maximum_price.final_price.currency - 货币代码（例如 'CNY', 'USD'）。
 * @param {number} priceRange.maximum_price.final_price.value - 最终价格的数值。
 * @param {Object} priceRange.maximum_price.regular_price - 原价格对象。
 * @param {string} priceRange.maximum_price.regular_price.currency - 货币代码（例如 'CNY', 'USD'）。
 * @param {number} priceRange.maximum_price.regular_price.value - 原价格的数值。
 *
 * @returns {Object} 返回提取的价格信息。
 * @returns {boolean} return.isDiscount - 是否存在促销价。
 * @returns {Object} return.finalPrice - 最终价格对象。
 * @returns {string} return.finalPrice.currency - 最终价格的货币代码。
 * @returns {number} return.finalPrice.value - 最终价格的数值。
 * @returns {Object} return.regularPrice - 原价格对象。
 * @returns {string} return.regularPrice.currency - 原价格的货币代码。
 * @returns {number} return.regularPrice.value - 原价格的数值。
 * @returns {string} return.currency - 价格的货币代码。
 *
 * @throws {Error} 如果 `priceRange.maximum_price` 缺失，抛出错误。
 */

const useProductPrice = (priceRange: PriceRanges) => {
  if (!priceRange?.maximum_price) {
    throw new Error('缺失价格参数信息');
  }

  /**
   * 是否有促销价
   */
  const isDiscount = useMemo(() => {
    return priceRange?.maximum_price?.discount?.amount_off > 0;
  }, [priceRange]);

  /**
   * 最终价格
   */
  const finalPrice = useMemo(() => {
    return priceRange?.maximum_price?.final_price;
  }, [priceRange]);

  /**
   * 原价
   */
  const regularPrice = useMemo(() => {
    return priceRange?.maximum_price?.regular_price;
  }, [priceRange]);

  /**
   * 货币符号
   */
  const currency = useMemo(() => {
    return priceRange?.maximum_price?.final_price?.currency;
  }, [priceRange]);

  return { isDiscount, finalPrice, regularPrice, currency };
};

export default useProductPrice;
