import { useCallback } from 'react';

import { useLoadingContext, useToastContext } from '../components';
import {
  useLazyCheckOrderPayQuery,
  useLazyGetAfterSalesQuery,
  useLazyGetAvailablePaymentsQuery,
  useLazyGetOrdersQuery,
  useLazyGetOrderStatusQuery,
} from '../services';
import { OrderListItem } from '../typings';
import { resolveCatchMessage, sleep } from '../utils';

/**
 * 订单 Hook
 */
const useUserOrder = () => {
  const toast = useToastContext();
  const loading = useLoadingContext();

  const [checkOrderPayQuery] = useLazyCheckOrderPayQuery();
  const [getPayments] = useLazyGetAvailablePaymentsQuery();
  const [getOrders, { isFetching: isGetOrdersLoading }] = useLazyGetOrdersQuery();
  const [getOrderStatus, { isFetching: isGetOrderStatusLoading }] = useLazyGetOrderStatusQuery();
  const [getAfterSales, { isFetching: isGetAfterSalesLoading }] = useLazyGetAfterSalesQuery();

  /**
   * 查询可用支付方式
   */
  const getAvailablePayments = useCallback(
    async (amount: number) => {
      await sleep(500);
      loading.show();
      return getPayments({
        amount,
      })
        .unwrap()
        .then(async res => {
          await sleep(500);
          loading.hide();
          return res?.avaliablePayments;
        })
        .catch(async error => {
          await sleep(500);
          loading.hide();
          await sleep(500);
          toast.show({
            icon: 'fail',
            content: resolveCatchMessage(error) as string,
          });
        });
    },
    [getPayments, loading, toast],
  );

  /**
   * 查询订单支付状态
   */
  const checkOrderPayInfo = useCallback(
    async (orderId: string) => {
      loading.show();
      return checkOrderPayQuery({
        orderId,
        action: 'create',
      })
        .unwrap()
        .then(async res => {
          await sleep(500);
          loading.hide();
          return res?.check_order_pay;
        })
        .catch(async error => {
          await sleep(500);
          loading.hide();
          await sleep(500);
          toast.show({
            icon: 'fail',
            content: resolveCatchMessage(error) as string,
          });
        });
    },
    [checkOrderPayQuery, loading, toast],
  );

  /**
   * 查询订单的所有状态
   */
  const getOrderStatusInfo = useCallback(
    async () => {
      return getOrderStatus({})
        .unwrap()
        .then(async res => {
          await sleep(500);
          return res?.customer?.orders?.status_tab;
        })
        .catch(async error => {
          await sleep(500);
          await sleep(500);
          toast.show({
            icon: 'fail',
            content: resolveCatchMessage(error) as string,
          });
          return null;
        })
    }, [getOrderStatus, toast]
  )

  /**
   * 查询用户订单列表
   */
  const getUserOrders = useCallback(
    async (
      page = 1,
      pageSize = 2,
      activeKey = '',
    ) => {
      return getOrders({
        filter: {
          status: {
            eq: activeKey,
          },
        },
        currentPage: page,
        pageSize: pageSize,
      })
        .unwrap()
        .then(async res => {
          await sleep(500);

          return {
            orderList: res?.customer?.orders?.items as OrderListItem[],
            total: res?.customer?.orders?.total_count || 0
          };
        })
        .catch(async error => {
          await sleep(500);
          await sleep(500);
          toast.show({
            icon: 'fail',
            content: resolveCatchMessage(error) as string,
          });
          return null;
        });
    }, [getOrders, toast]
  )

  /**
   * 查询售后订单列表
   */
  const getAfterSalesOrders = useCallback(
    async (
      page = 1,
      pageSize = 2,
    ) => {
      return getAfterSales({
        currentPage: page,
        pageSize: pageSize,
      })
        .unwrap()
        .then(async res => {
          await sleep(500);
          return {
            orderList: res?.requisitionList?.items as OrderListItem[],
            total: res?.requisitionList?.total_count || 0
          };
        })
        .catch(async error => {
          await sleep(500);
          await sleep(500);
          toast.show({
            icon: 'fail',
            content: resolveCatchMessage(error) as string,
          })

          return null;
        })
    }, [getAfterSales, toast]
  )

  return {
    checkOrderPayInfo,
    getAvailablePayments,
    getUserOrders,
    getOrderStatusInfo,
    getAfterSalesOrders,
    isGetOrdersLoading,
    isGetOrderStatusLoading,
    isGetAfterSalesLoading,
  };
};

export default useUserOrder;
