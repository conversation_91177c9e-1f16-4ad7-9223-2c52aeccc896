/* eslint-disable @typescript-eslint/no-explicit-any */
import { useCallback, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { useLoadingContext, useToastContext } from '../components';
import { CustomerShippingAddressInput } from '../graphql/generated/graphql';
import {
  useAddUserAddressMutation,
  useDeleteUserAddressMutation,
  useLazyGetUserAddressesQuery,
  useUpdateUserAddressMutation,
} from '../services';
import {
  selectUserAllAddress,
  setUserAllAddress,
  userAddressDefaultSelector,
  userAddressFirstSelector,
} from '../store';
import { resolveCatchMessage, sleep } from '../utils';

/**
 * useUserAddress Hook
 *
 * 用于管理和获取用户的地址信息
 *
 * @returns {Object} 包含用户地址相关状态和操作方法的对象
 * @property {boolean} haveUserAddresses - 用户是否有地址。
 * @property {Object|null} firstUserAddress - 用户的第一个地址。
 * @property {Object|null} userAddressDefault - 用户的默认地址。
 * @property {Function} fetchUserAddresses - 获取用户的地址列表并保存到 store。
 * @property {Array} userAllAddress - 当前用户的所有地址列表。
 * @property {boolean} fetchUserAddressesLoading - 是否正在加载用户地址。
 * @property {Function} getUserAddressById - 根据 addressId 获取对应地址的方法。
 * @property {Function} addUserAddress - 新增用户地址的方法。
 * @property {boolean} addUserAddressLoading - 新增用户地址的加载状态。
 * @property {Function} updateUserAddress - 更新用户地址的方法。
 * @property {boolean} updateUserAddressLoading - 更新用户地址的加载状态。
 * @property {Function} deleteUserAddress - 删除用户地址的方法。
 * @property {boolean} deleteUserAddressLoading - 删除用户地址的加载状态。
 */
const useUserAddress = () => {
  const dispatch = useDispatch();
  const toast = useToastContext();
  const loading = useLoadingContext();

  const userAllAddress = useSelector(selectUserAllAddress);
  const userAddressDefault = useSelector(userAddressDefaultSelector);
  // 获取用户第 1 个地址
  const firstUserAddress = useSelector(userAddressFirstSelector);

  const [getUserAddressQuery, { isFetching: fetchUserAddressesLoading }] =
    useLazyGetUserAddressesQuery();
  const [updateUserAddressMutation, { isLoading: updateUserAddressLoading }] =
    useUpdateUserAddressMutation();
  const [deleteUserAddressMutation, { isLoading: deleteUserAddressLoading }] =
    useDeleteUserAddressMutation();
  const [addUserAddressMutation, { isLoading: addUserAddressLoading }] =
    useAddUserAddressMutation();

  /**
   * 用户是否有地址
   */
  const haveUserAddresses = useMemo(() => {
    return !!userAllAddress.length;
  }, [userAllAddress]);

  /**
   * 获取用户地址，保存到 store
   */
  const fetchUserAddresses = useCallback(async () => {
    try {
      const response = await getUserAddressQuery({}).unwrap();

      if (response?.customer?.customer_addresses?.length) {
        dispatch(setUserAllAddress(response.customer.customer_addresses));

        return response.customer.customer_addresses;
      }

      return null;
    } catch (error: any) {
      toast.show({
        icon: 'fail',
        content: resolveCatchMessage(error) as string,
      });

      return null;
    }
  }, [getUserAddressQuery, dispatch, toast]);

  /**
   * 根据 addressId 获取 address 数据
   */
  const getUserAddressById = useCallback(
    (addressId: string) => {
      return userAllAddress.find(address => address.address_id === addressId);
    },
    [userAllAddress],
  );

  /**
   * 新增用户地址
   */
  const addUserAddress = useCallback(
    async (address: CustomerShippingAddressInput) => {
      try {
        loading.show();
        const response = await addUserAddressMutation({
          shippingAddress: address,
        }).unwrap();
        await sleep(500);
        await loading.hide();

        if (response?.add_shipping_address?.length) {
          return response?.add_shipping_address;
        }

        return null;
      } catch (error: any) {
        await sleep(500);
        await loading.hide();
        await sleep(500);
        toast.show({
          icon: 'fail',
          content: resolveCatchMessage(error) as string,
        });

        return null;
      }
    },
    [addUserAddressMutation, loading, toast],
  );

  /*
   * 更新用户地址
   */
  const updateUserAddress = useCallback(
    async (address: CustomerShippingAddressInput) => {
      try {
        loading.show();
        const response = await updateUserAddressMutation({
          shippingAddress: address,
        }).unwrap();
        await sleep(500);
        await loading.hide();

        if (response?.edit_shipping_address?.length) {
          return response.edit_shipping_address;
        }

        return null;
      } catch (error: any) {
        await sleep(500);
        await loading.hide();
        await sleep(500);
        toast.show({
          icon: 'fail',
          content: resolveCatchMessage(error) as string,
        });

        return null;
      }
    },
    [updateUserAddressMutation, loading, toast],
  );

  /**
   * 删除用户地址
   */
  const deleteUserAddress = useCallback(
    async (addressId: number) => {
      try {
        loading.show();
        const response = await deleteUserAddressMutation({
          addressId,
        }).unwrap();
        await sleep(500);
        await loading.hide();

        return response?.delete_shipping_address;
      } catch (error: any) {
        await sleep(500);
        await loading.hide();
        await sleep(500);
        toast.show({
          icon: 'fail',
          content: resolveCatchMessage(error) as string,
        });

        return null;
      }
    },
    [deleteUserAddressMutation, loading, toast],
  );

  return {
    haveUserAddresses,
    firstUserAddress,
    userAddressDefault,
    fetchUserAddresses,
    userAllAddress,
    fetchUserAddressesLoading,
    getUserAddressById,
    addUserAddress,
    addUserAddressLoading,
    updateUserAddress,
    updateUserAddressLoading,
    deleteUserAddress,
    deleteUserAddressLoading,
  };
};

export default useUserAddress;
