"use client"

import { useEffect, useMemo, useRef, useState } from 'react';

import { calcLeftTime, formatTime } from '../utils';


/**
 * useCountdown Hook
 *
 * 倒计时
 *
 * @param {number} startTime - 倒计时的开始时间
 * @param {number} endTime - 倒计时的结束时间
 * @param {number} [interval=1000] - 每次更新的时间间隔，默认为1000毫秒
 * @param {function} onEnd - 倒计时结束时调用的回调函数
 * @returns {[number, { total: number, days: number, hours: number, minutes: number, seconds: number }]}
 * 返回剩余时间（秒）和格式化的时间对象，格式为：
 * {
 *   total: 总秒数,
 *   days: 剩余天数,
 *   hours: 剩余小时数,
 *   minutes: 剩余分钟数,
 *   seconds: 剩余秒数
 * }
 */
const useCountdown = (startTime: number, endTime: number, interval = 1000, onEnd: () => void) => {
  /**
   * 初始化剩余时间
   */
  const [timeLeft, setTimeLeft] = useState(() =>
    calcLeftTime(startTime, endTime),
  );
  const timeLeftRef = useRef(timeLeft);
  const onEndRef = useRef(onEnd);
  const intervalRef = useRef<ReturnType<typeof setInterval> | null>(null);

  useEffect(() => {
    onEndRef.current = onEnd;
  }, [onEnd]);

  useEffect(() => {
    timeLeftRef.current = timeLeft;
  }, [timeLeft]);

  useEffect(() => {
    const initialTimeLeft = calcLeftTime(startTime, endTime);
    setTimeLeft(initialTimeLeft);

    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    const updateTime = () => {
      if (timeLeftRef.current <= 0) {
        if (onEndRef.current) {
          onEndRef.current();
        }

        // 倒计时结束时清除 interval
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
        }
      } else {
        setTimeLeft(prevTimeLeft => prevTimeLeft - 1);
      }
    };

    if (initialTimeLeft > 0) {
      intervalRef.current = setInterval(updateTime, interval);
    }

    //  组件卸载时清除 interval
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [startTime, endTime, interval]);

  const formattedRes = useMemo(() => formatTime(timeLeft), [timeLeft]);

  return formattedRes;
};

export default useCountdown;
