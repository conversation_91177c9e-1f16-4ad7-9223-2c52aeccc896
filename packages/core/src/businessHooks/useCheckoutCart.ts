import { useCallback } from 'react'
import { useDispatch } from 'react-redux'

import { useLazyGetCheckoutCartQuery } from '../services'
import { setAllCartProducts, TCheckoutCartProductItems } from '../store'
import { formatProducts, TCatchMessage } from '../utils'
import { useRateLimitHandler } from '../hooks'

/**
 * 获取购物车信息 Hook
 */
const useCheckoutCart = () => {
  const dispatch = useDispatch()

  const [getCheckoutCartQuery, { isLoading }] = useLazyGetCheckoutCartQuery()

  // 限流处理 - toast模式
  const rateLimitHandler = useRateLimitHandler({
    mode: 'toast',
  })

  /**
   * 获取购物车信息
   */
  const fetchCheckoutCart = useCallback(async () => {
    try {
      const response = await getCheckoutCartQuery({}).unwrap()
      if (response?.customer?.shipping_cart?.items) {
        dispatch(setAllCartProducts(formatProducts(response?.customer.shipping_cart.items as TCheckoutCartProductItems)))
      }
    } catch (error) {
      // 尝试处理限流错误
      const isRateLimitHandled = rateLimitHandler.handleError(error as TCatchMessage)

      // 如果不是限流错误，只记录到控制台，不显示给用户
      if (!isRateLimitHandled) {
        console.error('useCheckoutCart error:', error)
      }
    }
  }, [dispatch, getCheckoutCartQuery, rateLimitHandler])

  return { fetchCheckoutCart, isLoading }
}

export default useCheckoutCart
