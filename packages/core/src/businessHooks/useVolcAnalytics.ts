'use client'
import { useCallback, useEffect } from 'react'

import { useToastContext } from '../components'
import { TRACK_EVENT } from '../constants/global'
import { resolveCatchMessage, TCatchMessage } from '../utils'
import { analytics, VolcAnalyticsConfig } from '../utils/volcAnalytics'

// 自定义事件类型
export type AnalyticsEventType = (typeof TRACK_EVENT)[keyof typeof TRACK_EVENT]

// 事件属性接口
export interface AnalyticsEventProperties {
  [key: string]: string | number | boolean | undefined | null
}

// 火山引擎埋点配置
const analyticsConfig: VolcAnalyticsConfig = {
  app_id: Number(process.env.NEXT_PUBLIC_VOLC_ANALYTICS_APP_ID), // 替换为您的应用 ID
  channel_domain: process.env.NEXT_PUBLIC_VOLC_ANALYTICS_CHANNEL_DOMAIN, // 替换为您的私有化部署地址
  log: process.env.NODE_ENV === 'development', // 开发环境开启日志
  autotrack: true, // 开启全埋点
  enable_debug: process.env.NODE_ENV === 'development', // 开发环境开启调试模式
}

let isInitialized = false

/**
 * 自定义 Hook 用于事件上报
 * @returns 事件上报方法
 */
function useVolcAnalytics() {
  const toast = useToastContext()

  /**
   * 初始化火山引擎埋点
   */
  useEffect(() => {
    if (isInitialized) return

    try {
      analytics.init(analyticsConfig)
      isInitialized = true
    } catch (error) {
      console.error('火山引擎埋点初始化失败:', error)
      // 初始化失败时清除标记，允许下次重试
      isInitialized = false
    }
  }, [])

  /**
   * 发送自定义事件
   * @param eventName 事件名称
   * @param properties 事件属性
   */
  const reportEvent = useCallback(
    (eventName: AnalyticsEventType, properties?: AnalyticsEventProperties) => {
      try {
        analytics.track(eventName, properties)
      } catch (error) {
        toast.show({
          icon: 'fail',
          content: resolveCatchMessage(error as TCatchMessage) as string,
        })
      }
    },
    [toast],
  )

  /**
   * 页面跳转前上报自定义事件
   * @param eventName 事件名称
   * @param properties 事件属性
   */
  const advanceReportEvent = useCallback(
    (eventName: AnalyticsEventType, properties?: AnalyticsEventProperties) => {
      try {
        analytics.advanceTrack(eventName, properties)
      } catch (error) {
        toast.show({
          icon: 'fail',
          content: resolveCatchMessage(error as TCatchMessage) as string,
        })
      }
    },
    [toast],
  )

  return {
    reportEvent,
    advanceReportEvent,
  }
}

export default useVolcAnalytics
