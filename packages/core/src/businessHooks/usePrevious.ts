'use client'
import { useRef } from 'react'

const defaultShouldUpdate = <T>(a: T | undefined, b: T) => !Object.is(a, b)

/**
 * usePrevious Hook
 *
 * 用于获取上一次的 state 值。
 *
 * @param {any} state 当前的状态值。
 * @param {function} [shouldUpdate] 可选的比较函数，用于决定是否更新上一次的值。
 * 默认为一个浅比较函数（Object.is）。
 * @returns {any} 返回上一次的状态值。
 */
const usePrevious = <T>(
  state: T,
  shouldUpdate: (a: T | undefined, b: T) => boolean = defaultShouldUpdate,
) => {
  const prevRef = useRef<T | undefined>()
  const curRef = useRef<T | undefined>()

  if (shouldUpdate(curRef.current, state)) {
    prevRef.current = curRef.current
    curRef.current = state
  }

  return prevRef.current
}

export default usePrevious
