/* --------------------------------- 全局静态变量 --------------------------------- */

/**
 * 存储 token key
 */
export const KEY_TOKEN = 'token'

/**
 * 发送 token 的 header key
 */
export const KEY_AUTHORIZATION = 'Authorization'

/**
 * 发送 x token 的 header key
 */
export const KEY_X_AUTHORIZATION = 'X-Authorization'

/**
 * passport 登录 code
 */
export const PASSPORT_LOGIN_CODE = 'code'

/**
 * 限流 quid key
 */
export const QUID_KEY = 'quid'

/**
 * 跳转链接类型
 */
export const URL_TYPE = {
  product: 'PRODUCT',
  category: 'CATEGORY',
  cms_page: 'CMS_PAGE',
  custom: 'CUSTOM',
  special: 'SPECIAL',
  mini_program: 'MINI_PROGRAM',
  default: 'Home',
}

/**
 * 跳转链接类型路由
 */
export const URL_TYPE_ROUTE = {
  PRODUCT: 'CatalogProduct',
  CATEGORY: 'CatalogCategory',
  CMS_PAGE: 'CMSPage',
  SPECIAL: 'ThematicArea',
  special: 'ThematicArea',
  CUSTOM: 'WebView',
  DEFAULT: 'Home',
}

/**
 * 产品配送类型
 *
 */
export const DELIVERY_METHOD_TYPE = {
  store_pickup: 'delivery_method_store_pickup',
  express: 'delivery_method_express',
  none: 'delivery_method_none',
  default: 'default',
}

/**
 * 产品配送类型标题
 */
export const DELIVERY_METHOD_TEXT = {
  [DELIVERY_METHOD_TYPE.store_pickup]: '自提商品',
  [DELIVERY_METHOD_TYPE.express]: '邮寄商品',
  [DELIVERY_METHOD_TYPE.none]: '虚拟商品',
  [DELIVERY_METHOD_TYPE.default]: '商品',
}

/**
 * 订单状态
 */
export const ORDER_STATUS = [
  {
    status: 'all',
    label: '全部订单',
  },
  {
    status: 'pending',
    label: '待付款',
  },
  {
    status: 'processing',
    label: '待发货',
  },
  {
    status: 'to_be_used_shipped_pending_pickup',
    label: '待收货/使用',
  },
  {
    status: 'delivered_completed',
    label: '已完成',
  },
  {
    status: 'order_requisition',
    label: '订单售后',
  },
]

/**
 * 产品允许使用N币的状态
 */
export const ALLOWED_TO_USE_N_COIN = [
  'payment_method_cash_ncoin', // N币抵扣状态
  'payment_method_ncoin', // 纯N币状态
]

/**
 * 纯N币产品状态
 */
export const ONLY_N_COIN = 'payment_method_ncoin'

/**
 * 地址空提示
 */
export const addressValidFields = [
  { key: 'receive_name', message: '收货人不能为空' },
  { key: 'receive_phone', message: '手机号不能为空' },
  { key: 'area', message: '收货地址不能为空' },
  { key: 'street', message: '详细地址不能为空' },
]

/**
 * 路由
 */
export const ROUTE = {
  home: 'Index',
  catalogCategory: 'CatalogCategory',
  catalogCategoryAll: 'CatalogCategoryAll',
  search: 'Search',
  searchResult: 'SearchResult',
  catalogProduct: 'CatalogProduct',
  productComment: 'ProductComment',
  checkoutCart: 'CheckoutCart',
  checkout: 'Checkout',
  checkoutResult: 'CheckoutResult',
  checkoutPending: 'CheckoutPending',
  checkoutPaying: 'CheckoutPaying',
  account: 'Account',
  accountAddress: 'AccountAddress',
  accountAddressAdd: 'AccountAddressAdd',
  accountAddressEdit: 'AccountAddressEdit',
  accountOrder: 'AccountOrder',
  accountOrderDetail: 'AccountOrderDetail',
  accountOrderReturn: 'AccountOrderReturn',
  accountOrderReturnDetail: 'AccountOrderReturnDetail',
  accountOrderReturnTrack: 'AccountOrderReturnTrack',
  pcOrderReturn: 'PcOrderReturn',
  accountCoupon: 'AccountCoupon',
  cmsPage: 'CMSPage',
  webView: 'WebView',
  rateLimit: 'RateLimit',
  thematicArea: 'ThematicArea',
}

/**
 * 埋点事件类型
 */
export const TRACK_EVENT = {
  // 曝光事件 (Exposure Events)
  shop_home_page_exposure: 'shop_home_page_exposure',
  shop_classfication_exposure: 'shop_classfication_exposure',
  shop_small_classfication_exposure: 'shop_small_classfication_exposure',
  shop_search_exposure: 'shop_search_exposure',
  shop_search_result_exposure: 'shop_search_result_exposure',
  shop_commodity_details_exposure: 'shop_commodity_details_exposure',
  shop_cart_page_exposure: 'shop_cart_page_exposure',
  shop_commodity_settlement_exposure: 'shop_commodity_settlement_exposure',
  shop_profile_page_exposure: 'shop_profile_page_exposure',
  shop_order_page_exposure: 'shop_order_page_exposure',
  shop_coupon_page_exposure: 'shop_coupon_page_exposure',
  shop_address_info_exposure: 'shop_address_info_exposure',
  shop_homepage_top_banner_picture_exposure: 'shop_homepage_top_banner_picture_exposure',
  shop_homepage_banner_picture_exposure: 'shop_homepage_banner_picture_exposure',

  // 首页点击事件 (Homepage Click Events)
  shop_homepage_search_button_click: 'shop_homepage_search_button_click',
  shop_homepage_cart_button_click: 'shop_homepage_cart_button_click',
  shop_homepage_profile_button_click: 'shop_homepage_profile_button_click',
  shop_homepage_tab_click: 'shop_homepage_tab_click',
  shop_homepage_more_button_click: 'shop_homepage_more_button_click',
  shop_homepage_top_banner_picture_click: 'shop_homepage_top_banner_picture_click',
  shop_homepage_top_banner_detail_button_click: 'shop_homepage_top_banner_detail_button_click',
  shop_homepage_banner_picture_click: 'shop_homepage_banner_picture_click',
  shop_homepage_category_view_all_button_click: 'shop_homepage_category_view_all_button_click',
  shop_homepage_category_product_picture_click: 'shop_homepage_category_product_picture_click',
  shop_homepage_discount_view_all_button_click: 'shop_homepage_discount_view_all_button_click',
  shop_homepage_discount_product_picture_click: 'shop_homepage_discount_product_picture_click',

  // 分类页点击事件 (Category Click Events)
  shop_category_left_tab_click: 'shop_category_left_tab_click',
  shop_category_top_tab_click: 'shop_category_top_tab_click',
  shop_category_product_picture_click: 'shop_category_product_picture_click',
  shop_sort_tab_click: 'shop_sort_tab_click',
  shop_sort_option_click: 'shop_sort_option_click',
  shop_sort_product_picture_click: 'shop_sort_product_picture_click',

  // 搜索页点击事件 (Search Click Events)
  shop_searchpage_search_click: 'shop_searchpage_search_click',
  shop_searchpage_cancel_button_click: 'shop_searchpage_cancel_button_click',
  shop_searchpage_product_name_click: 'shop_searchpage_product_name_click',
  shop_searchpage_switch_products_click: 'shop_searchpage_switch_products_click',
  shop_searchpage_product_picture_click: 'shop_searchpage_product_picture_click',

  // 搜索结果页点击事件 (Search Result Click Events)
  shop_searchresult_search_click: 'shop_searchresult_search_click',
  shop_searchresult_cancel_button_click: 'shop_searchresult_cancel_button_click',
  shop_searchresult_product_picture_click: 'shop_searchresult_product_picture_click',

  // 商品详情页点击事件 (Product Detail Click Events)
  shop_details_cart_add_click: 'shop_details_cart_add_click',
  shop_details_buy_now_click: 'shop_details_buy_now_click',
  shop_details_cart_button_click: 'shop_details_cart_button_click',

  // 购物车页点击事件 (Cart Click Events)
  shop_cart_tab_click: 'shop_cart_tab_click',
  shop_cart_checkout_click: 'shop_cart_checkout_click',
  shop_cart_product_picture_click: 'shop_cart_product_picture_click',

  // 结算页点击事件 (Payment Click Events)
  shop_payment_purchase_click: 'shop_payment_purchase_click',
  shop_payment_method_click: 'shop_payment_method_click',

  // 生活页点击事件 (Life Page Click Events)
  shop_life_kingkong_click: 'shop_life_kingkong_click',
}

/**
 * 登录跳转来源页面 URL
 */
export const LOGIN_REFERER_URL = 'login_referer_url'


/**
 * 精准限流接口列表
 */
export const PRECISE_RATE_LIMIT_APIS = [
  "addProductsToShippingCart", // 添加产品到购物车
  "addCartItemsToCheckout", // 添加产品到结算页
  "setPaymentMethodAndPlaceOrder", // 设置支付方式并下单
  "buyNowToCheckout", // 立即购买
  "getSearchRelationWords", // 获取搜索关联词
  "getSearchProducts", // 获取搜索产品
  "getCartQty", // 获取购物车总数量
]
