'use client'

import { useState } from 'react'
import { Button } from 'antd-mobile'
import { useRateLimitHandler, useToastContext } from '@ninebot/core'

/**
 * 限流功能测试页面 - H5版本
 */
const TestRateLimitPage = () => {
  const toast = useToastContext()
  
  // 按钮模式限流处理
  const buttonRateLimit = useRateLimitHandler({
    mode: 'button',
    originalText: '提交订单',
  })

  // Toast模式限流处理
  const toastRateLimit = useRateLimitHandler({
    mode: 'toast',
  })

  const [loading, setLoading] = useState(false)

  // 模拟限流错误
  const simulateRateLimitError = () => {
    const mockError = {
      data: {
        errorType: 'precise-rate-limit',
        error: '请求过于频繁，请稍后再试！',
        retryMs: 5000,
      }
    }
    return mockError
  }

  // 模拟普通错误
  const simulateNormalError = () => {
    const mockError = {
      data: '网络错误，请重试'
    }
    return mockError
  }

  // 测试按钮模式限流
  const handleButtonTest = async () => {
    setLoading(true)
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 模拟限流错误
      throw simulateRateLimitError()
    } catch (error) {
      // 尝试处理限流错误
      const isRateLimitHandled = buttonRateLimit.handleError(error as any)
      
      // 如果不是限流错误，显示普通错误提示
      if (!isRateLimitHandled) {
        toast.show({
          icon: 'fail',
          content: '操作失败，请重试',
        })
      }
    } finally {
      setLoading(false)
    }
  }

  // 测试Toast模式限流
  const handleToastTest = async () => {
    setLoading(true)
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 模拟限流错误
      throw simulateRateLimitError()
    } catch (error) {
      // 尝试处理限流错误
      const isRateLimitHandled = toastRateLimit.handleError(error as any)
      
      // 如果不是限流错误，显示普通错误提示
      if (!isRateLimitHandled) {
        toast.show({
          icon: 'fail',
          content: '操作失败，请重试',
        })
      }
    } finally {
      setLoading(false)
    }
  }

  // 测试普通错误
  const handleNormalErrorTest = async () => {
    setLoading(true)
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 模拟普通错误
      throw simulateNormalError()
    } catch (error) {
      // 尝试处理限流错误
      const isRateLimitHandled = buttonRateLimit.handleError(error as any)
      
      // 如果不是限流错误，显示普通错误提示
      if (!isRateLimitHandled) {
        toast.show({
          icon: 'fail',
          content: '这是一个普通错误',
        })
      }
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="p-4">
      <h1 className="mb-6 text-xl font-bold">限流功能测试页面</h1>
      
      <div className="space-y-6">
        <div className="rounded-lg border border-gray-200 p-4">
          <h2 className="mb-3 text-lg font-semibold">按钮模式限流测试</h2>
          <p className="mb-4 text-sm text-gray-600">
            点击按钮后会模拟限流错误，按钮会显示倒计时并禁用
          </p>
          <Button
            className="nb-button w-full"
            color="primary"
            loading={loading}
            disabled={buttonRateLimit.isDisabled}
            onClick={handleButtonTest}
          >
            {buttonRateLimit.buttonText}
          </Button>
          <div className="mt-2 text-xs text-gray-500">
            状态: {buttonRateLimit.isRateLimited ? '限流中' : '正常'} | 
            倒计时: {buttonRateLimit.countdown}s
          </div>
        </div>

        <div className="rounded-lg border border-gray-200 p-4">
          <h2 className="mb-3 text-lg font-semibold">Toast模式限流测试</h2>
          <p className="mb-4 text-sm text-gray-600">
            点击按钮后会模拟限流错误，会显示Toast提示
          </p>
          <Button
            className="nb-button w-full"
            loading={loading}
            onClick={handleToastTest}
          >
            测试Toast限流
          </Button>
        </div>

        <div className="rounded-lg border border-gray-200 p-4">
          <h2 className="mb-3 text-lg font-semibold">普通错误测试</h2>
          <p className="mb-4 text-sm text-gray-600">
            点击按钮后会模拟普通错误，应该显示普通错误提示
          </p>
          <Button
            className="nb-button w-full"
            loading={loading}
            onClick={handleNormalErrorTest}
          >
            测试普通错误
          </Button>
        </div>

        <div className="rounded-lg border border-gray-200 p-4">
          <h2 className="mb-3 text-lg font-semibold">重置功能测试</h2>
          <p className="mb-4 text-sm text-gray-600">
            如果按钮处于限流状态，可以点击重置按钮清除限流状态
          </p>
          <Button
            className="nb-button w-full"
            onClick={buttonRateLimit.reset}
            disabled={!buttonRateLimit.isRateLimited}
          >
            重置限流状态
          </Button>
        </div>
      </div>
    </div>
  )
}

export default TestRateLimitPage
